import React, { useState, useEffect } from "react";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Check,
  X,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";

const Daybook = () => {
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("جمعه"); // Track active sheet
  const [jamaRows, setJamaRows] = useState([]);
  const [namRows, setNamRows] = useState([]);

  // د نمونه ډیټا
  const sampleDaybook = {
    id: 1,
    date: new Date().toLocaleDateString("fa-AF"),
    page_number: 15,
    status: "خلاص", // خلاص یا تړل شوی
    total_jamah: 125000,
    total_naam: 98000,
    balance: 27000,
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۰:۳۰",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      time: "۱۰:۴۵",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۱:۱۵",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    loadDaybook();
  }, []);

  const loadDaybook = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCurrentDaybook(sampleDaybook);
      setTransactions(sampleTransactions);

      // Initialize with one empty row
      const initialRow = {
        id: 1,
        customer_name: "",
        description: "",
        amount: "",
        currency: "افغانۍ",
        time: "",
        balance: "",
      };

      setJamaRows([initialRow]);
      setNamRows([{ ...initialRow }]);
      setLoading(false);
    }, 1000);
  };

  // Handle cell input changes
  const handleCellChange = (rowIndex, field, value, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered (only if time is empty)
        if (field === "amount" && value && !newRows[rowIndex].time) {
          const now = new Date();
          const hours = now.getHours().toString().padStart(2, "0");
          const minutes = now.getMinutes().toString().padStart(2, "0");
          newRows[rowIndex].time = `${hours}:${minutes}`;
        }

        return newRows;
      });
    } else {
      setNamRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered (only if time is empty)
        if (field === "amount" && value && !newRows[rowIndex].time) {
          const now = new Date();
          const hours = now.getHours().toString().padStart(2, "0");
          const minutes = now.getMinutes().toString().padStart(2, "0");
          newRows[rowIndex].time = `${hours}:${minutes}`;
        }

        return newRows;
      });
    }
  };

  // Handle Enter key press to move to next row
  const handleKeyPress = (e, rowIndex, field, sheetType) => {
    if (e.key === "Enter") {
      e.preventDefault();

      // Always add new row when pressing Enter, regardless of current row content
      const currentRows = sheetType === "جمعه" ? jamaRows : namRows;

      // If this is the last row, add a new one
      if (rowIndex === currentRows.length - 1) {
        addSingleRow(sheetType);
      }

      // Focus next row's first input
      setTimeout(() => {
        const nextRowInput = document.querySelector(
          `[data-row="${rowIndex + 1}"][data-field="${
            sheetType === "جمعه" ? "jama" : "nam"
          }_customer"][data-sheet="${sheetType}"]`
        );
        if (nextRowInput) {
          nextRowInput.focus();
        }
      }, 100);
    }
  };

  // Add single row when needed
  const addSingleRow = (sheetType) => {
    const newRow = {
      id: (sheetType === "جمعه" ? jamaRows.length : namRows.length) + 1,
      customer_name: "",
      description: "",
      amount: "",
      currency: "افغانۍ",
      time: "",
      balance: "",
    };

    if (sheetType === "جمعه") {
      setJamaRows((prev) => [...prev, newRow]);
    } else {
      setNamRows((prev) => [...prev, newRow]);
    }
  };

  // Delete row function
  const deleteRow = (rowIndex, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => prev.filter((_, index) => index !== rowIndex));
    } else {
      setNamRows((prev) => prev.filter((_, index) => index !== rowIndex));
    }
  };

  const toggleDaybookStatus = () => {
    setCurrentDaybook((prev) => ({
      ...prev,
      status: prev.status === "خلاص" ? "تړل شوی" : "خلاص",
    }));
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* د ورځني کتاب سرلیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-4 mt-2'>
                <span className='text-gray-600 pashto-text'>
                  نیټه: {currentDaybook?.date}
                </span>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    currentDaybook?.status === "خلاص"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {currentDaybook?.status}
                </span>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button variant='outline' icon={Calendar} onClick={() => {}}>
                نوی ورځني کتاب
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "خلاص" ? "danger" : "success"
                }
                icon={currentDaybook?.status === "خلاص" ? Lock : Unlock}
                onClick={toggleDaybookStatus}
              >
                {currentDaybook?.status === "خلاص" ? "تړل" : "خلاصول"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                <div>
                  <p className='text-sm text-green-600 pashto-text'>
                    ټوله جمعه
                  </p>
                  <p className='text-xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-red-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                <div>
                  <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                  <p className='text-xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Save className='h-8 w-8 text-blue-600 ml-3' />
                <div>
                  <p className='text-sm text-blue-600 pashto-text'>میزان</p>
                  <p
                    className={`text-xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Calendar className='h-8 w-8 text-gray-600 ml-3' />
                <div>
                  <p className='text-sm text-gray-600 pashto-text'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-xl font-bold text-gray-700'>
                    {transactions.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Two Separate Tables Side by Side */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-4'>
          {/* Jama Table */}
          <div className='bg-white rounded-lg shadow-md border-2 border-green-200'>
            <div className='bg-green-50 px-4 py-3 border-b border-green-200'>
              <h3 className='text-sm font-bold text-green-800 pashto-text text-center'>
                جمعه (اخیستل)
              </h3>
            </div>
            <div className='overflow-x-auto'>
              <table className='min-w-full border-collapse text-xs'>
                <thead className='bg-green-50'>
                  <tr>
                    <th className='border border-green-300 px-2 py-1 text-center text-xs font-medium text-green-700 w-8'>
                      #
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-right text-xs font-medium text-green-700 pashto-text min-w-[100px]'>
                      پیرودونکی
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-right text-xs font-medium text-green-700 pashto-text min-w-[120px]'>
                      تفصیل
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-right text-xs font-medium text-green-700 pashto-text min-w-[80px]'>
                      اندازه
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-right text-xs font-medium text-green-700 pashto-text min-w-[60px]'>
                      پیسې
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-center text-xs font-medium text-green-700 min-w-[60px]'>
                      وخت
                    </th>
                    <th className='border border-green-300 px-2 py-1 text-center text-xs font-medium text-green-700 w-8'>
                      ×
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white'>
                  {jamaRows.map((row, index) => (
                    <tr key={row.id} className='hover:bg-green-25'>
                      <td className='border border-green-300 px-2 py-1 text-center text-xs text-green-700'>
                        {index + 1}
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.customer_name}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "customer_name",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_customer", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_customer'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='نوم'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.description}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "description",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_description", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_description'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='تفصیل'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.amount}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "amount",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_amount", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_amount'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          dir='ltr'
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <select
                          value={row.currency}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "currency",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_currency", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_currency'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                        >
                          <option value='افغانۍ'>افغانۍ</option>
                          <option value='ډالر'>ډالر</option>
                          <option value='یورو'>یورو</option>
                          <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                        </select>
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='time'
                          value={row.time}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "time",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_time", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_time'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1 text-center'>
                        <button
                          onClick={() => deleteRow(index, "جمعه")}
                          className='p-1 text-red-600 hover:bg-red-100 rounded'
                          title='قطار ړنګ کړئ'
                        >
                          <X className='h-3 w-3' />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Nam Table */}
          <div className='bg-white rounded-lg shadow-md border-2 border-red-200'>
            <div className='bg-red-50 px-4 py-3 border-b border-red-200'>
              <h3 className='text-sm font-bold text-red-800 pashto-text text-center'>
                نام (ورکول)
              </h3>
            </div>
            <div className='overflow-x-auto'>
              <table className='min-w-full border-collapse text-xs'>
                <thead className='bg-red-50'>
                  <tr>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 w-8'>
                      #
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[100px]'>
                      پیرودونکی
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[120px]'>
                      تفصیل
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[80px]'>
                      اندازه
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[60px]'>
                      پیسې
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 min-w-[60px]'>
                      وخت
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 w-8'>
                      ×
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white'>
                  {namRows.map((row, index) => (
                    <tr key={row.id} className='hover:bg-red-25'>
                      <td className='border border-red-300 px-2 py-1 text-center text-xs text-red-700'>
                        {index + 1}
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.customer_name}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "customer_name",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_customer", "نام")
                          }
                          data-row={index}
                          data-field='nam_customer'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='نوم'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.description}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "description",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_description", "نام")
                          }
                          data-row={index}
                          data-field='nam_description'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='تفصیل'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.amount}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "amount",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_amount", "نام")
                          }
                          data-row={index}
                          data-field='nam_amount'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          dir='ltr'
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <select
                          value={row.currency}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "currency",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_currency", "نام")
                          }
                          data-row={index}
                          data-field='nam_currency'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                        >
                          <option value='افغانۍ'>افغانۍ</option>
                          <option value='ډالر'>ډالر</option>
                          <option value='یورو'>یورو</option>
                          <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                        </select>
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='time'
                          value={row.time}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "time",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_time", "نام")
                          }
                          data-row={index}
                          data-field='nam_time'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1 text-center'>
                        <button
                          onClick={() => deleteRow(index, "نام")}
                          className='p-1 text-red-600 hover:bg-red-100 rounded'
                          title='قطار ړنګ کړئ'
                        >
                          <X className='h-3 w-3' />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Daybook;
